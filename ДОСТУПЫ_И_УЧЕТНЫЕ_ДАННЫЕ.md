# 🔐 ChartGenius v3.0 - Доступы и учетные данные

## ⚠️ ВНИМАНИЕ: КОНФИДЕНЦИАЛЬНАЯ ИНФОРМАЦИЯ
**Этот файл содержит критически важные учетные данные. Не публикуйте его в открытых репозиториях!**

---

## 🌐 PRODUCTION СЕРВЕР

### 🖥️ **Oracle Cloud Instance:**
- **IP адрес**: *************
- **Регион**: eu-frankfurt-1
- **Тип**: VM.Standard.E2.1.Micro (Always Free)
- **ОС**: Ubuntu 24.04.2 LTS
- **Пользователь**: ubuntu

### 🔑 **SSH доступ:**
```bash
# Команда для подключения:
ssh -i chartgenius_v3_oracle_key ubuntu@*************

# Файл ключа: chartgenius_v3_oracle_key (в корне проекта)
# Права доступа: chmod 600 chartgenius_v3_oracle_key
```

### 📁 **Структура проекта на сервере:**
```
/home/<USER>/
├── chartgenius-v3/                 # Основной проект
│   ├── docker-compose-simple.yml   # Docker конфигурация
│   ├── .env                        # Переменные окружения
│   ├── backend_new/                # Backend код
│   ├── frontend/                   # Frontend код (собранный)
│   └── nginx.conf                  # Nginx конфигурация
```

---

## 🌐 ДОМЕНЫ И SSL

### 🔗 **Основной домен:**
- **URL**: https://chartgenius.online
- **SSL**: Let's Encrypt (автообновление)
- **Nginx**: Проксирование на localhost:8000

### 🔧 **n8n домен:**
- **URL**: https://chartgenius.ru
- **Назначение**: n8n workflows
- **Статус**: Активен, все workflows работают

---

## 🗄️ БАЗЫ ДАННЫХ

### 🐘 **PostgreSQL:**
- **Хост**: localhost (в Docker)
- **Порт**: 5432
- **База данных**: chartgenius_db
- **Пользователь**: chartgenius
- **Пароль**: chartgenius_password_2025
- **Подключение**: `postgresql://chartgenius:chartgenius_password_2025@localhost:5432/chartgenius_db`

### ⚡ **Redis:**
- **Хост**: localhost (в Docker)
- **Порт**: 6379
- **Пароль**: Не установлен
- **Подключение**: `redis://localhost:6379`

---

## 🤖 TELEGRAM BOT

### 📱 **Bot информация:**
- **Username**: @Chart_Genius_Prime_bot
- **Bot ID**: 7231133799
- **Display Name**: Chart Genius Prime

### 🔑 **API данные:**
- **Bot Token**: `**********************************************`
- **Webhook URL**: https://chartgenius.online/api/v1/telegram/webhook
- **API Base**: https://api.telegram.org/bot{token}/

### 🔧 **Команды для управления:**
```bash
# Получить информацию о боте:
curl "https://api.telegram.org/bot**********************************************/getMe"

# Проверить webhook:
curl "https://api.telegram.org/bot**********************************************/getWebhookInfo"

# Установить webhook:
curl -X POST "https://api.telegram.org/bot**********************************************/setWebhook" -d "url=https://chartgenius.online/api/v1/telegram/webhook"
```

---

## 🔧 N8N WORKFLOWS

### 🌐 **n8n Instance:**
- **URL**: https://chartgenius.ru
- **Пользователь**: DMITRII KOCHAROV
- **Email**: Не указан (вход через интерфейс)
- **документация**: https://docs.n8n.io/

### 📋 **Активные Workflows:**
1. **ChartGenius Payment Processing**
   - ID: Не указан
   - Статус: ✅ Active
   - Назначение: Обработка платежей и активация подписок

2. **ChartGenius Analysis Notification**
   - ID: Не указан
   - Статус: ✅ Active
   - Назначение: Отправка уведомлений о готовности анализа

3. **ChartGenius Telegram Bot Commands**
   - ID: Не указан
   - Статус: ✅ Active
   - Назначение: Обработка команд Telegram бота

### 🔗 **Webhook endpoints:**
```
# Примерные webhook URL (нужно уточнить в n8n):
https://chartgenius.ru/webhook/payment-processing
https://chartgenius.ru/webhook/analysis-notification
https://chartgenius.ru/webhook/telegram-bot
```

---

## 🧠 AI СЕРВИСЫ

### 🤖 **OpenAI API:**
- **Статус**: актуален
- **Текущий ключ**: актуален
- **Переменная**: OPENAI_API_KEY в .env файле
- **Модель**: gpt-o3-mini

### 📊 **CryptoCompare API:**
- **Статус**: актуален
- **Назначение**: Получение данных о криптовалютах
- **Переменная**: CRYPTO_API_KEY в .env файле

---

## 👤 ПОЛЬЗОВАТЕЛИ СИСТЕМЫ

### 👑 **Admin пользователь (проверить данные):**
- **Telegram**: @Dushnar
- **Telegram ID**: 299820674
- **User ID в БД**: 1
- **План подписки**: admin
- **Срок действия**: 2035-07-19 (10 лет)
- **Статус**: ✅ Active

### 📋 **Планы подписок в БД:**
```sql
-- Доступные планы:
SELECT * FROM plans;
-- free: {"daily_requests": 10, "features": ["basic_analysis"]}
-- premium: {"daily_requests": 100, "features": ["basic_analysis", "advanced_analysis", "ai_insights"]}
-- admin: {"daily_requests": -1, "monthly_requests": -1, "features": ["all"], "expires_at": null, "is_unlimited": true}
```
- **Tribute**: API_KEY в Tribute 67798073-f13f-40b5-9890-4b43b101
---

## 🐳 DOCKER КОНТЕЙНЕРЫ

### 📋 **Активные контейнеры:**
```bash
# Проверить статус:
cd /home/<USER>/chartgenius-v3
docker compose -f docker-compose-simple.yml ps

# Контейнеры:
# - chartgenius_v3_backend (FastAPI)
# - chartgenius_v3_postgres (PostgreSQL)
# - chartgenius_v3_redis (Redis)
# - chartgenius_v3_celery_worker (Celery - нестабилен)
```

### 🔧 **Команды управления:**
```bash
# Перезапуск всех сервисов:
docker compose -f docker-compose-simple.yml restart

# Перезапуск конкретного сервиса:
docker compose -f docker-compose-simple.yml restart backend

# Просмотр логов:
docker compose -f docker-compose-simple.yml logs backend --tail=50

# Вход в контейнер:
docker compose -f docker-compose-simple.yml exec backend bash
```

---

## 🔐 ПЕРЕМЕННЫЕ ОКРУЖЕНИЯ (.env)

### 📄 **Актуальный .env файл на сервере:**
```bash
# Database
DATABASE_URL=postgresql+asyncpg://chartgenius:chartgenius_password_2025@postgres:5432/chartgenius_db
POSTGRES_USER=chartgenius
POSTGRES_PASSWORD=chartgenius_password_2025
POSTGRES_DB=chartgenius_db

# Redis
REDIS_URL=redis://redis:6379

# API Keys
OPENAI_API_KEY=********************************************************************************************************************************************************************
TELEGRAM_BOT_TOKEN=**********************************************

# Application
ENVIRONMENT=production
SECRET_KEY=your-secret-key-here
API_V1_PREFIX=/api/v1

# CORS (упрощенный формат)
ALLOWED_ORIGINS=https://chartgenius.online,https://t.me

# Celery
CELERY_BROKER_URL=redis://redis:6379
CELERY_RESULT_BACKEND=redis://redis:6379
```

---

## 🔧 NGINX КОНФИГУРАЦИЯ

### 📄 **Основной конфиг (/etc/nginx/sites-available/chartgenius.online):**
```nginx
server {
    listen 80;
    server_name chartgenius.online;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name chartgenius.online;

    ssl_certificate /etc/letsencrypt/live/chartgenius.online/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chartgenius.online/privkey.pem;

    # Frontend
    location / {
        root /home/<USER>/chartgenius-v3/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Health check
    location /health {
        proxy_pass http://localhost:8000;
    }
}
```

---

## 🚀 КОМАНДЫ БЫСТРОГО СТАРТА

### 🔄 **Перезапуск всей системы:**
```bash
# Подключиться к серверу:
ssh -i chartgenius_v3_oracle_key ubuntu@*************

# Перейти в проект:
cd /home/<USER>/chartgenius-v3

# Перезапустить Docker контейнеры:
docker compose -f docker-compose-simple.yml down
docker compose -f docker-compose-simple.yml up -d

# Перезапустить Nginx:
sudo systemctl restart nginx

# Проверить статус:
docker compose -f docker-compose-simple.yml ps
sudo systemctl status nginx
```

### 🔍 **Диагностика проблем:**
```bash
# Проверить логи backend:
docker compose -f docker-compose-simple.yml logs backend --tail=50

# Проверить логи Nginx:
sudo tail -f /var/log/nginx/error.log

# Проверить SSL сертификат:
sudo certbot certificates

# Тест API:
curl https://chartgenius.online/health
curl -X POST https://chartgenius.online/api/v1/telegram/webhook -H "Content-Type: application/json" -d '{"test": true}'
```

---

## 📞 КОНТАКТЫ И ПОДДЕРЖКА

### 👨‍💻 **Разработчик:**
- **Telegram**: @Dushnar
- **Роль**: Product Owner / Admin

### 🔧 **Техническая поддержка:**
- **Сервер**: Oracle Cloud Always Free
- **Мониторинг**: Ручной (через SSH)
- **Бэкапы**: Не настроены (требуется настройка)

---

**⚠️ ВАЖНО: Регулярно обновляйте пароли и API ключи для безопасности!**

*Последнее обновление: 25.07.2025*
