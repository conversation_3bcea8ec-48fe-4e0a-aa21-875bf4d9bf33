[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Критические исправления DESCRIPTION:Устранение блокирующих проблем для продакшн развертывания
--[ ] NAME:Обновление OpenAI API ключа DESCRIPTION:Получить новый OpenAI API ключ и обновить переменные окружения во всех конфигурациях
--[ ] NAME:Исправление Docker конфигураций DESCRIPTION:Обновить docker-compose.production.yml, исправить пути и переменные окружения
--[ ] NAME:Проверка аутентификации DESCRIPTION:Протестировать оба вида аутентификации: Telegram WebApp и Login Widget
-[ ] NAME:Подготовка локальной среды DESCRIPTION:Настройка и тестирование всех компонентов локально перед развертыванием
--[ ] NAME:Локальная сборка контейнеров DESCRIPTION:Собрать все Docker образы локально и протестировать их работу
--[ ] NAME:Тестирование микросервисов DESCRIPTION:Запустить и протестировать все микросервисы через docker-compose.microservices.yml
--[ ] NAME:Проверка интеграции с n8n DESCRIPTION:Протестировать webhook интеграцию между API Gateway и n8n
-[ ] NAME:Подготовка Oracle Cloud сервера DESCRIPTION:Очистка и настройка продакшн сервера для развертывания
--[ ] NAME:Очистка сервера DESCRIPTION:Остановить и удалить старые контейнеры, очистить Docker образы
--[ ] NAME:Обновление системы DESCRIPTION:Обновить Oracle Linux, Docker и docker-compose до последних версий
--[ ] NAME:Настройка переменных окружения DESCRIPTION:Создать .env файл с продакшн конфигурацией на сервере
-[ ] NAME:Развертывание базовой инфраструктуры DESCRIPTION:Развертывание PostgreSQL, Redis и API Gateway
--[ ] NAME:Развертывание PostgreSQL DESCRIPTION:Запустить PostgreSQL контейнер, инициализировать базу данных
--[ ] NAME:Развертывание Redis DESCRIPTION:Запустить Redis контейнер для кэширования
--[ ] NAME:Развертывание API Gateway DESCRIPTION:Запустить API Gateway контейнер и проверить health check
-[ ] NAME:Развертывание микросервисов DESCRIPTION:Поэтапное развертывание AI Analysis, Data Fetcher, Technical Analysis, Prompt Builder
--[ ] NAME:Развертывание AI Analysis DESCRIPTION:Запустить AI Analysis сервис (приоритет 1)
--[ ] NAME:Развертывание Data Fetcher DESCRIPTION:Запустить Data Fetcher сервис для получения OHLCV данных
--[ ] NAME:Развертывание Technical Analysis DESCRIPTION:Запустить Technical Analysis сервис для расчета индикаторов
-[ ] NAME:Активация n8n workflows DESCRIPTION:Активация и настройка n8n оркестрации на chartgenius.ru
--[ ] NAME:Активация Master Orchestrator DESCRIPTION:Активировать основной n8n workflow для обработки запросов
--[ ] NAME:Активация API Gateway Integration DESCRIPTION:Активировать workflow для интеграции с API Gateway
--[ ] NAME:Настройка webhook endpoints DESCRIPTION:Настроить webhook URL в n8n для связи с chartgenius.online
-[ ] NAME:Интеграционное тестирование DESCRIPTION:Полное тестирование системы и мониторинг производительности
--[ ] NAME:Тестирование полной цепочки DESCRIPTION:Протестировать полный путь: Frontend → API Gateway → n8n → Микросервисы
--[ ] NAME:Мониторинг ресурсов DESCRIPTION:Настроить мониторинг CPU, RAM, дискового пространства на Oracle Free Tier
--[ ] NAME:Проверка аутентификации в продакшне DESCRIPTION:Протестировать Telegram WebApp и Login Widget аутентификацию
--[ ] NAME:Проверка AI анализа DESCRIPTION:Протестировать работу AI анализа с обновленным OpenAI API ключом
-[ ] NAME:Тестирование интерфейса DESCRIPTION:Тестирование интерфейса на наличие ошибок и корректной работы всех функций
--[ ] NAME:Тестирование личного кабинета DESCRIPTION:Тестирование личного кабинета на наличие ошибок и корректной работы всех функций
--[ ] NAME:Тестирование админ панели DESCRIPTION:проверка работы всех функций админ панели
